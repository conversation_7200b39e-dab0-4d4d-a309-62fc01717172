{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:35:08:358"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:01:391"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:09:399"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:22:3922"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:41:3941"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:42:08:428"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:44:58:4458"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:44:58:4458"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:45:11:4511"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:45:11:4511"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:45:21:4521"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:46:49:4649"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:46:49:4649"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:46:54:4654"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:46:54:4654"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:47:00:470"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:47:05:475"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:50:10:5010"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:50:15:5015"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:37:32:3732"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:37:32:3732"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:37:40:3740"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:15:4015"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:15:4015"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:40:21:4021"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:35:4035"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:35:4035"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:40:41:4041"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:43:03:433"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:43:03:433"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:43:08:438"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:28:4528"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:28:4528"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:45:34:4534"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:41:4541"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:41:4541"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:45:47:4547"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:52:4552"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:52:4552"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:45:58:4558"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:46:13:4613"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:46:13:4613"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:46:19:4619"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:48:14:4814"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:48:14:4814"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:30:4830"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 16:48:30:4830"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:30:4830"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:30:4830"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:48:35:4835"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:56:4956"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:56:4956"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:50:00:500"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:50:00:500"}
{"error":{"errno":-5,"code":"EIO","syscall":"write"},"level":"error","message":"Uncaught exception detected, shutting down...","timestamp":"2025-06-19 16:50:13:5013"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:50:34:5034"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:50:34:5034"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:50:34:5034"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 16:50:34:5034"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:50:39:5039"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:15:37:1537"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:15:37:1537"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:15:43:1543"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:46:1646"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:46:1646"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:16:51:1651"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:56:1656"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:56:1656"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:18:54:1854"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:18:54:1854"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:490:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:18:54:1854"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 17:18:54:1854"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:18:59:1859"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:20:32:2032"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:20:32:2032"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:20:37:2037"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:34:27:3427"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:34:27:3427"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:34:33:3433"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:34:49:3449"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:34:49:3449"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:34:56:3456"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:35:42:3542"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:35:42:3542"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:35:49:3549"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:36:17:3617"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:36:17:3617"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:36:23:3623"}
{"error":{"errno":-5,"code":"EIO","syscall":"write"},"level":"error","message":"Uncaught exception detected, shutting down...","timestamp":"2025-06-19 17:37:15:3715"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:37:25:3725"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:37:25:3725"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:37:32:3732"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:41:39:4139"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:41:39:4139"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:41:46:4146"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:42:41:4241"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:42:41:4241"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:42:47:4247"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:44:37:4437"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:44:37:4437"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:44:43:4443"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:50:24:5024"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:50:24:5024"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:50:30:5030"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:51:08:518"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:51:08:518"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:51:14:5114"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:51:32:5132"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:51:32:5132"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 17:51:38:5138"}
{"error":{"errno":-5,"code":"EIO","syscall":"write"},"level":"error","message":"Uncaught exception detected, shutting down...","timestamp":"2025-06-19 17:51:48:5148"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 18:53:12:5312"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 18:53:12:5312"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 18:53:19:5319"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 19:59:20:5920"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:05:39:539"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:05:49:549"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:07:27:727"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:10:46:1046"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:14:56:1456"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:56:07:567"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:56:43:5643"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:57:45:5745"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:58:01:581"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:58:17:5817"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:58:32:5832"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:58:44:5844"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:59:05:595"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:59:26:5926"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 20:59:59:5959"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:36:59:3659"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:37:29:3729"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:37:52:3752"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:38:04:384"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:39:26:3926"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:39:38:3938"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:39:51:3951"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:41:17:4117"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:41:33:4133"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 21:43:07:437"}
