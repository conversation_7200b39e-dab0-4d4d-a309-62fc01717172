{"error":{"errno":-5,"code":"<PERSON><PERSON>","syscall":"write"},"level":"error","message":"uncaughtException: write EIO\nError: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at WriteStream.Socket._writeGeneric (node:net:971:11)\n    at WriteStream.Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at WriteStream.Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at EventEmitter.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisService.ts:200:15)","stack":"Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at WriteStream.Socket._writeGeneric (node:net:971:11)\n    at WriteStream.Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at WriteStream.Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at EventEmitter.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisService.ts:200:15)","exception":true,"date":"Thu Jun 19 2025 16:50:13 GMT+0600 (Bangladesh Standard Time)","process":{"pid":47015,"uid":1000,"gid":1000,"cwd":"/media/gm-hridoy/study/programming-hero/green-uni-mind/backend","execPath":"/home/<USER>/.nvm/versions/node/v23.11.0/bin/node","version":"v23.11.0","argv":["/home/<USER>/.nvm/versions/node/v23.11.0/bin/node","src/server.ts"],"memoryUsage":{"rss":140111872,"heapTotal":59150336,"heapUsed":55510544,"external":38913192,"arrayBuffers":36429985}},"os":{"loadavg":[2.17,2.55,3],"uptime":7042.03},"trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"WriteStream.Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"WriteStream.Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":570,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":499,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"WriteStream.Writable.write","line":508,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.log","line":384,"method":"log","native":false},{"column":15,"file":"/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisService.ts","function":null,"line":200,"method":null,"native":false}],"timestamp":"2025-06-19 16:50:13:5013"}
{"error":{"errno":-5,"code":"EIO","syscall":"write"},"level":"error","message":"uncaughtException: write EIO\nError: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at WriteStream.Socket._writeGeneric (node:net:971:11)\n    at WriteStream.Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at WriteStream.Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/passport.ts:32:19","stack":"Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at WriteStream.Socket._writeGeneric (node:net:971:11)\n    at WriteStream.Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at WriteStream.Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/passport.ts:32:19","exception":true,"date":"Thu Jun 19 2025 17:37:15 GMT+0600 (Bangladesh Standard Time)","process":{"pid":70431,"uid":1000,"gid":1000,"cwd":"/media/gm-hridoy/study/programming-hero/green-uni-mind/backend","execPath":"/home/<USER>/.nvm/versions/node/v23.11.0/bin/node","version":"v23.11.0","argv":["/home/<USER>/.nvm/versions/node/v23.11.0/bin/node","src/server.ts"],"memoryUsage":{"rss":*********,"heapTotal":60461056,"heapUsed":56630064,"external":38829867,"arrayBuffers":36308828}},"os":{"loadavg":[8.69,10.16,7.12],"uptime":9863.32},"trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"WriteStream.Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"WriteStream.Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":570,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":499,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"WriteStream.Writable.write","line":508,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.log","line":384,"method":"log","native":false},{"column":19,"file":"/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/passport.ts","function":null,"line":32,"method":null,"native":false}],"timestamp":"2025-06-19 17:37:15:3715"}
{"error":{"errno":-5,"code":"EIO","syscall":"write"},"level":"error","message":"uncaughtException: write EIO\nError: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at WriteStream.Socket._writeGeneric (node:net:971:11)\n    at WriteStream.Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at WriteStream.Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app.ts:177:11","stack":"Error: write EIO\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at WriteStream.Socket._writeGeneric (node:net:971:11)\n    at WriteStream.Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:570:12)\n    at _write (node:internal/streams/writable:499:10)\n    at WriteStream.Writable.write (node:internal/streams/writable:508:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app.ts:177:11","exception":true,"date":"Thu Jun 19 2025 17:51:48 GMT+0600 (Bangladesh Standard Time)","process":{"pid":77285,"uid":1000,"gid":1000,"cwd":"/media/gm-hridoy/study/programming-hero/green-uni-mind/backend","execPath":"/home/<USER>/.nvm/versions/node/v23.11.0/bin/node","version":"v23.11.0","argv":["/home/<USER>/.nvm/versions/node/v23.11.0/bin/node","src/server.ts"],"memoryUsage":{"rss":136683520,"heapTotal":57839616,"heapUsed":54060168,"external":38783604,"arrayBuffers":36303996}},"os":{"loadavg":[2.65,2.46,3.96],"uptime":10736.9},"trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"WriteStream.Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"WriteStream.Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":570,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":499,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"WriteStream.Writable.write","line":508,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.log","line":384,"method":"log","native":false},{"column":11,"file":"/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app.ts","function":null,"line":177,"method":null,"native":false}],"timestamp":"2025-06-19 17:51:48:5148"}
